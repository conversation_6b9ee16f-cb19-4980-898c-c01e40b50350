import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  <PERSON>rkles,
  Brain,
  Users,
  Code,
  FileText,
  Search,
  Globe,
  Zap,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { EmmaAgenticSeekChat } from '@/components/unified-chat/emma-agenticseek-chat';
import { agentApiService } from '@/services/agent-api-service';
import { useToast } from '@/hooks/use-toast';

export const EmmaAgenticSeekPage: React.FC = () => {
  const [systemStatus, setSystemStatus] = useState<{
    status: string;
    agents_available: number;
    system_ready: boolean;
  } | null>(null);
  const [isCheckingStatus, setIsCheckingStatus] = useState(true);
  const { toast } = useToast();

  // Check AgenticSeek status on load
  useEffect(() => {
    checkSystemStatus();
  }, []);

  const checkSystemStatus = async () => {
    setIsCheckingStatus(true);
    try {
      const status = await agentApiService.getAgenticSeekStatus();
      setSystemStatus(status);
      
      if (status.system_ready) {
        toast({
          title: '✅ Sistema listo',
          description: `Emma está online con ${status.agents_available} agentes disponibles`,
          variant: 'default'
        });
      }
    } catch (error) {
      console.error('Error checking AgenticSeek status:', error);
      toast({
        title: '⚠️ Estado del sistema',
        description: 'No se pudo verificar el estado. Emma puede estar iniciando.',
        variant: 'destructive'
      });
    } finally {
      setIsCheckingStatus(false);
    }
  };

  const agents = [
    {
      name: 'CasualAgent',
      icon: Users,
      description: 'Conversaciones naturales y consultas generales',
      color: 'bg-blue-500'
    },
    {
      name: 'CoderAgent',
      icon: Code,
      description: 'Desarrollo de código y soluciones técnicas',
      color: 'bg-green-500'
    },
    {
      name: 'FileAgent',
      icon: FileText,
      description: 'Gestión y análisis de archivos',
      color: 'bg-yellow-500'
    },
    {
      name: 'PlannerAgent',
      icon: Brain,
      description: 'Planificación estratégica y análisis',
      color: 'bg-purple-500'
    },
    {
      name: 'BrowserAgent',
      icon: Globe,
      description: 'Investigación web y búsqueda de información',
      color: 'bg-red-500'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-pink-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="w-16 h-16 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
              <Sparkles className="h-8 w-8 text-white" />
            </div>
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                Emma AI
              </h1>
              <p className="text-gray-600">Powered by AgenticSeek</p>
            </div>
          </div>
          
          <p className="text-lg text-gray-700 max-w-2xl mx-auto">
            La nueva Emma con sistema multi-agente avanzado. Capaz de manejar consultas complejas 
            delegando tareas a agentes especializados.
          </p>

          {/* System Status */}
          <div className="mt-6 flex items-center justify-center gap-4">
            {isCheckingStatus ? (
              <Badge variant="outline" className="flex items-center gap-2">
                <Loader2 className="h-3 w-3 animate-spin" />
                Verificando sistema...
              </Badge>
            ) : systemStatus ? (
              <>
                <Badge 
                  variant={systemStatus.system_ready ? "default" : "destructive"}
                  className="flex items-center gap-2"
                >
                  {systemStatus.system_ready ? (
                    <CheckCircle className="h-3 w-3" />
                  ) : (
                    <AlertCircle className="h-3 w-3" />
                  )}
                  {systemStatus.system_ready ? 'Sistema Listo' : 'Sistema Iniciando'}
                </Badge>
                <Badge variant="outline">
                  {systemStatus.agents_available} Agentes Disponibles
                </Badge>
              </>
            ) : (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={checkSystemStatus}
                className="flex items-center gap-2"
              >
                <Zap className="h-3 w-3" />
                Verificar Estado
              </Button>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Agents Panel */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Agentes Especializados
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {agents.map((agent) => (
                  <div key={agent.name} className="flex items-start gap-3 p-3 rounded-lg border border-gray-100 hover:border-gray-200 transition-colors">
                    <div className={`w-8 h-8 rounded-full ${agent.color} flex items-center justify-center flex-shrink-0`}>
                      <agent.icon className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <h4 className="font-medium text-sm">{agent.name}</h4>
                      <p className="text-xs text-gray-600">{agent.description}</p>
                    </div>
                  </div>
                ))}
                
                <div className="mt-6 p-4 bg-purple-50 rounded-lg border border-purple-100">
                  <h4 className="font-medium text-sm text-purple-800 mb-2">
                    🧠 Inteligencia Distribuida
                  </h4>
                  <p className="text-xs text-purple-600">
                    Emma analiza tu consulta y delega automáticamente al agente más adecuado 
                    para darte la mejor respuesta posible.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Chat Interface */}
          <div className="lg:col-span-2">
            <Card className="h-[600px]">
              <EmmaAgenticSeekChat className="h-full" />
            </Card>
          </div>
        </div>

        {/* Features */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="text-center p-6">
            <Brain className="h-12 w-12 mx-auto mb-4 text-purple-500" />
            <h3 className="font-semibold mb-2">Inteligencia Multi-Agente</h3>
            <p className="text-sm text-gray-600">
              Sistema avanzado que coordina múltiples agentes especializados para resolver consultas complejas.
            </p>
          </Card>
          
          <Card className="text-center p-6">
            <Zap className="h-12 w-12 mx-auto mb-4 text-yellow-500" />
            <h3 className="font-semibold mb-2">Respuestas Especializadas</h3>
            <p className="text-sm text-gray-600">
              Cada agente está optimizado para tareas específicas, garantizando respuestas precisas y relevantes.
            </p>
          </Card>
          
          <Card className="text-center p-6">
            <Search className="h-12 w-12 mx-auto mb-4 text-blue-500" />
            <h3 className="font-semibold mb-2">Investigación Avanzada</h3>
            <p className="text-sm text-gray-600">
              Capacidad de búsqueda web en tiempo real y análisis de información para respuestas actualizadas.
            </p>
          </Card>
        </div>

        {/* Example Queries */}
        <div className="mt-12">
          <Card>
            <CardHeader>
              <CardTitle>💡 Ejemplos de consultas para probar</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h4 className="font-medium text-sm">Marketing y Estrategia:</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• "Crea un plan de marketing para una startup de tecnología"</li>
                    <li>• "Analiza las tendencias de marketing digital 2024"</li>
                    <li>• "Diseña una campaña para redes sociales"</li>
                  </ul>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium text-sm">Contenido y Análisis:</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• "Escribe un artículo sobre inteligencia artificial"</li>
                    <li>• "Analiza la competencia en el sector fintech"</li>
                    <li>• "Crea contenido para LinkedIn sobre liderazgo"</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
