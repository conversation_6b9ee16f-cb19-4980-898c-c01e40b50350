import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Send,
  User,
  Bot,
  Loader2,
  Sparkles,
  Brain,
  Zap,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { agentApiService } from '@/services/agent-api-service';

interface EmmaAgenticSeekChatProps {
  className?: string;
}

interface Message {
  id: string;
  type: 'user' | 'emma';
  content: string;
  agentUsed?: string;
  thinkingProcess?: string;
  timestamp: string;
  status?: 'success' | 'error';
  screenshot?: string; // URL del screenshot
  hasWebActivity?: boolean; // Indica si Emma navegó por la web
}

export const EmmaAgenticSeekChat: React.FC<EmmaAgenticSeekChatProps> = ({
  className = ''
}) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showThinking, setShowThinking] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  // Auto scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Cleanup screenshot URLs when component unmounts
  useEffect(() => {
    return () => {
      messages.forEach(message => {
        if (message.screenshot) {
          URL.revokeObjectURL(message.screenshot);
        }
      });
    };
  }, [messages]);

  // Send message to AgenticSeek
  const handleSendMessage = async () => {
    if (!input.trim() || isLoading) return;

    const userMessage: Message = {
      id: `user-${Date.now()}`,
      type: 'user',
      content: input.trim(),
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    const currentInput = input.trim();
    setInput('');
    setIsLoading(true);

    try {
      // Call AgenticSeek API
      const response = await agentApiService.agenticSeekChat(currentInput);

      // Check if Emma performed web activity (browser agent used)
      const hasWebActivity = response.agent_used?.toLowerCase().includes('browser') ||
                            response.response.toLowerCase().includes('navegando') ||
                            response.response.toLowerCase().includes('buscando') ||
                            response.response.toLowerCase().includes('visitando');

      let screenshotUrl = undefined;

      // If Emma used web browsing, try to get screenshot
      if (hasWebActivity) {
        try {
          const screenshotBlob = await agentApiService.getAgenticSeekScreenshot();
          if (screenshotBlob) {
            screenshotUrl = URL.createObjectURL(screenshotBlob);
          }
        } catch (error) {
          console.log('Could not fetch screenshot:', error);
        }
      }

      const emmaMessage: Message = {
        id: `emma-${Date.now()}`,
        type: 'emma',
        content: response.response,
        agentUsed: response.agent_used,
        thinkingProcess: response.thinking_process,
        timestamp: new Date().toISOString(),
        status: response.status,
        screenshot: screenshotUrl,
        hasWebActivity: hasWebActivity
      };

      setMessages(prev => [...prev, emmaMessage]);

      if (response.status === 'success') {
        toast({
          title: '✅ Emma respondió',
          description: `Agente usado: ${response.agent_used || 'Emma'}`,
          variant: 'default'
        });
      } else {
        toast({
          title: '⚠️ Respuesta con advertencias',
          description: 'Emma respondió pero hubo algunos problemas técnicos',
          variant: 'destructive'
        });
      }

    } catch (error) {
      console.error('Error sending message to AgenticSeek:', error);

      const errorMessage: Message = {
        id: `error-${Date.now()}`,
        type: 'emma',
        content: 'Lo siento, estoy experimentando dificultades técnicas. Por favor intenta de nuevo más tarde.',
        timestamp: new Date().toISOString(),
        status: 'error'
      };

      setMessages(prev => [...prev, errorMessage]);

      toast({
        title: '❌ Error de conexión',
        description: 'No pude conectar con Emma. Verifica tu conexión.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Toggle thinking process display
  const toggleThinking = (messageId: string) => {
    setShowThinking(showThinking === messageId ? null : messageId);
  };

  return (
    <div className={`flex flex-col h-full bg-white ${className}`}>
      {/* Header */}
      <div className="border-b bg-gradient-to-r from-purple-50 to-pink-50 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
              <Sparkles className="h-5 w-5 text-white" />
            </div>
            <div>
              <span className="font-bold text-lg text-gray-800">Emma AI</span>
              <p className="text-xs text-gray-600">Powered by AgenticSeek</p>
            </div>
            <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
              <Zap className="h-3 w-3 mr-1" />
              AgenticSeek
            </Badge>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 && (
          <div className="text-center text-gray-500 mt-8">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-purple-100 to-pink-100 flex items-center justify-center">
              <Sparkles className="h-8 w-8 text-purple-500" />
            </div>
            <p className="text-lg font-medium text-gray-800">¡Hola! Soy Emma</p>
            <p className="text-sm text-gray-600">Tu asistente de marketing con IA avanzada</p>
            <p className="text-xs text-gray-500 mt-2">Pregúntame sobre estrategias de marketing, contenido, análisis y más</p>
          </div>
        )}

        {messages.map((message) => (
          <div key={message.id} className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
            <Card className={`max-w-[85%] ${
              message.type === 'user'
                ? 'bg-blue-500 text-white'
                : message.status === 'error'
                ? 'bg-red-50 border-red-200'
                : 'bg-white border'
            }`}>
              <CardContent className="p-4">
                {/* Message header */}
                <div className="flex items-center gap-2 mb-3">
                  {message.type === 'user' ? (
                    <User className="h-4 w-4" />
                  ) : (
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
                        <Bot className="h-4 w-4 text-white" />
                      </div>
                      <div className="flex flex-col">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-sm">Emma</span>
                          {message.status === 'success' && <CheckCircle className="h-3 w-3 text-green-500" />}
                          {message.status === 'error' && <AlertCircle className="h-3 w-3 text-red-500" />}
                        </div>
                        <span className="text-xs text-gray-500">
                          {message.agentUsed ? `Agente: ${message.agentUsed}` : 'AI Marketing Assistant'}
                        </span>
                      </div>
                    </div>
                  )}
                </div>

                {/* Message content */}
                <div className="text-sm whitespace-pre-wrap">
                  {message.content}
                </div>

                {/* Screenshot display */}
                {message.screenshot && (
                  <div className="mt-3 pt-3 border-t border-gray-100">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      <span className="text-xs text-gray-600 font-medium">Emma navegó por la web</span>
                    </div>
                    <div className="relative rounded-lg overflow-hidden border border-gray-200 bg-gray-50">
                      <img
                        src={message.screenshot}
                        alt="Screenshot de navegación web"
                        className="w-full max-w-md h-auto rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer"
                        onClick={() => window.open(message.screenshot, '_blank')}
                      />
                      <div className="absolute top-2 right-2">
                        <Badge variant="secondary" className="text-xs bg-white/80 backdrop-blur-sm">
                          Screenshot
                        </Badge>
                      </div>
                    </div>
                  </div>
                )}

                {/* Web activity indicator without screenshot */}
                {message.hasWebActivity && !message.screenshot && (
                  <div className="mt-3 pt-3 border-t border-gray-100">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                      <span className="text-xs text-gray-600">Emma realizó búsquedas web</span>
                    </div>
                  </div>
                )}

                {/* Thinking process button */}
                {message.type === 'emma' && message.thinkingProcess && (
                  <div className="mt-3 pt-3 border-t border-gray-100">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleThinking(message.id)}
                      className="text-xs text-purple-600 hover:text-purple-700"
                    >
                      <Brain className="h-3 w-3 mr-1" />
                      {showThinking === message.id ? 'Ocultar proceso' : 'Ver cómo pensé'}
                    </Button>

                    {showThinking === message.id && (
                      <div className="mt-2 p-3 bg-purple-50 rounded-lg border border-purple-100">
                        <p className="text-xs font-medium text-purple-700 mb-1">Proceso de pensamiento:</p>
                        <p className="text-xs text-purple-600 whitespace-pre-wrap">{message.thinkingProcess}</p>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        ))}

        {isLoading && (
          <div className="flex justify-start">
            <Card className="bg-purple-50 border-purple-200">
              <CardContent className="p-4 flex items-center gap-3">
                <Loader2 className="h-5 w-5 animate-spin text-purple-600" />
                <div>
                  <p className="text-sm font-medium text-purple-800">Emma está analizando...</p>
                  <p className="text-xs text-purple-600">Procesando con AgenticSeek</p>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="border-t bg-gray-50 p-4">
        <div className="flex gap-3 items-end">
          <div className="flex-1">
            <Input
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder="Pregúntale a Emma sobre marketing, estrategias, contenido..."
              onKeyPress={(e) => e.key === 'Enter' && !e.shiftKey && handleSendMessage()}
              disabled={isLoading}
              className="min-h-[40px]"
            />
          </div>
          <Button
            onClick={handleSendMessage}
            disabled={isLoading || !input.trim()}
            size="lg"
            className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};
